{"main": "./dist/main/main.js", "packageManager": "pnpm@9.10.0", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "dev": "electron-vite dev --watch"}, "dependencies": {"axios": "^1.10.0", "electron-store": "^10.1.0"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^2.0.0", "@electron-toolkit/eslint-config-ts": "^2.0.0", "@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/tsconfig": "^1.0.1", "@electron-toolkit/utils": "^3.0.0", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.3", "@types/node": "^20.19.2", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "electron": "^34.1.1", "electron-builder": "^26.0.12", "electron-vite": "^3.1.0", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.5", "lucide-react": "^0.468.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router": "^7.6.3", "rimraf": "^6.0.1", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "vite": "^6.3.5"}, "engines": {"node": ">=20.x"}}