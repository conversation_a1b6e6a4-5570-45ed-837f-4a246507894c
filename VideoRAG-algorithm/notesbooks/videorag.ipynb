{"cells": [{"cell_type": "code", "execution_count": null, "id": "c1e12bf6-a8ae-4fec-9fbd-99ea74bcc563", "metadata": {}, "outputs": [], "source": ["import os\n", "import logging\n", "import warnings\n", "import multiprocessing\n", "import nest_asyncio\n", "    \n", "nest_asyncio.apply()\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "logging.getLogger(\"httpx\").setLevel(logging.WARNING)\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = '0'\n", "\n", "from videorag._llm import  openai_config, openai_4o_mini_config, azure_openai_config, ollama_config\n", "from videorag import VideoRAG, QueryParam\n"]}, {"cell_type": "code", "execution_count": null, "id": "f04cee24-fd8e-41dc-93ea-888229d2a9af", "metadata": {}, "outputs": [], "source": ["video_paths = [\n", "        '/path/to/your/video.mp4',\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1a8b5f05-12e8-4b53-b84c-e4555fc99022", "metadata": {}, "outputs": [], "source": ["multiprocessing.set_start_method('spawn')"]}, {"cell_type": "code", "execution_count": null, "id": "62f20b71-3ae4-4db9-9023-cdba818ef5e8", "metadata": {}, "outputs": [], "source": ["videorag = VideoRAG(llm=ollama_config, working_dir=f\"./videorag-workdir/lexington\")"]}, {"cell_type": "code", "execution_count": null, "id": "7539e57b-11c2-42a9-b8f1-2363e0f561df", "metadata": {}, "outputs": [], "source": ["# To build\n", "videorag.insert_video(video_path_list=video_paths)"]}, {"cell_type": "code", "execution_count": null, "id": "6a938da7-5c33-44a3-a66e-a2e07636ce70", "metadata": {}, "outputs": [], "source": ["# To query\n", "videorag.load_caption_model(debug=False)\n", "param = QueryParam(mode=\"videorag\")"]}, {"cell_type": "code", "execution_count": null, "id": "15d51fb9-06e9-44e0-83ef-f3269c50480f", "metadata": {}, "outputs": [], "source": ["query = \"What are the Lexington school construction options\"\n", "param.wo_reference = False\n", "response = videorag.query(query=query, param=param)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "id": "4d517d22-a482-47a1-a4ac-89d0042b016f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda env:videorag]", "language": "python", "name": "conda-env-videorag-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}